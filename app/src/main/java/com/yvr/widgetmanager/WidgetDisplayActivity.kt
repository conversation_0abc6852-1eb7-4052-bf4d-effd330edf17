package com.yvr.widgetmanager

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.yvr.widgetmanager.manager.WidgetHostManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import com.yvr.widgetmanager.ui.screen.WidgetDisplayScreen
import com.yvr.widgetmanager.ui.theme.WidgetManagerTheme
import java.util.concurrent.ConcurrentHashMap

/**
 * 专门用于显示Widget的透明Activity
 */
class WidgetDisplayActivity : ComponentActivity() {

    private var TAG = "WidgetDisplayActivity"

    private lateinit var widgetHostManager: WidgetHostManager
    
    companion object {
        // 使用Map存储多个widget信息，支持多实例
        val widgetInfoMap = ConcurrentHashMap<String, AppWidgetInfo>()
        const val EXTRA_WIDGET_KEY = "widget_key"

        // 为每个实例生成唯一的HOST_ID
        private var nextHostId = 1000
        private fun getNextHostId(): Int {
            return synchronized(this) {
                nextHostId++
            }
        }
        /**
         * 获取widget信息
         */
        private fun getWidgetInfo(widgetKey: String): AppWidgetInfo? {
                return widgetInfoMap[widgetKey]
            }
        /**
         * 清理widget信息
         */
        private fun clearWidgetInfo(widgetKey: String) {
                widgetInfoMap.remove(widgetKey)
            }
    }

    private var widgetKey: String? = null
    private var hostId: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge()

        var displayId = 0;
        if (Build.VERSION.SDK_INT >= 30) {
            displayId = getDisplay().getDisplayId();
        }
        Log.d(TAG, "onCreate: displayId = $displayId");

        // 读取 Intent 配置
        val isNewDisplay = intent.getBooleanExtra("new_display_each", false)

        // 获取widget key
        widgetKey = intent.getStringExtra(EXTRA_WIDGET_KEY)
        Log.d(TAG, "onCreate: $widgetKey")

        if (widgetKey == null) {
            finish()
            return
        }
        // 获取widget信息
        val widgetInfo = getWidgetInfo(widgetKey!!)
        Log.d(TAG, "onCreate: $widgetInfo")

        if (widgetInfo == null) {
            finish()
            return
        }
        
        // 为每个实例生成唯一的hostId，避免多实例冲突
        hostId = getNextHostId()
        Log.d(TAG, "onCreate: hostId = $hostId")

        // 初始化WidgetHostManager，使用唯一的hostId
        widgetHostManager = WidgetHostManager(this, this, hostId)
        widgetHostManager.initialize()
        setContent {
            WidgetManagerTheme {
                WidgetDisplayScreen(
                    widgetInfo = widgetInfo,
                    widgetHostManager = widgetHostManager,
                    onBackClick = {
                        finish()
                    }
                )
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "onNewIntent: $widgetKey")
    }

    override fun onRestart() {
        super.onRestart()
        Log.d(TAG, "onRestart: $widgetKey")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume: $widgetKey, hostId: $hostId")
        // 确保WidgetHost在恢复时重新开始监听
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.startListening()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting widget host listening", e)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause: $widgetKey, hostId: $hostId")
        // 暂停时停止监听，但不销毁
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.stopListening()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping widget host listening", e)
            }
        }
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop: $widgetKey")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged: $widgetKey")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy: $widgetKey, hostId: $hostId")

        // 销毁WidgetHostManager
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.destroy()
                Log.d(TAG, "WidgetHostManager destroyed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error destroying WidgetHostManager", e)
            }
        }

        // 清理对应的widget信息
        widgetKey?.let { key ->
            clearWidgetInfo(key)
            Log.d(TAG, "Widget info cleared for key: $key")
        }
    }
}
