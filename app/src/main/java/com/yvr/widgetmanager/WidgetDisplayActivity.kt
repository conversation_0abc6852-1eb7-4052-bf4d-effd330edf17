package com.yvr.widgetmanager

import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.lifecycle.lifecycleScope
import com.yvr.widgetmanager.manager.WidgetHostManager
import com.yvr.widgetmanager.manager.WidgetPersistenceManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import com.yvr.widgetmanager.ui.screen.WidgetDisplayScreen
import com.yvr.widgetmanager.ui.theme.WidgetManagerTheme
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 专门用于显示Widget的透明Activity
 */
class WidgetDisplayActivity : ComponentActivity() {

    private var TAG = "WidgetDisplayActivity"

    private lateinit var widgetHostManager: WidgetHostManager
    private lateinit var persistenceManager: WidgetPersistenceManager
    
    companion object {
        // 使用Map存储多个widget信息，支持多实例
        val widgetInfoMap = ConcurrentHashMap<String, AppWidgetInfo>()
        const val EXTRA_WIDGET_KEY = "widget_key"

        // 为每个实例生成唯一的HOST_ID
        private var nextHostId = 1000
        private fun getNextHostId(): Int {
            return synchronized(this) {
                nextHostId++
            }
        }
        /**
         * 获取widget信息
         */
        private fun getWidgetInfo(widgetKey: String): AppWidgetInfo? {
                return widgetInfoMap[widgetKey]
            }
        /**
         * 清理widget信息
         */
        private fun clearWidgetInfo(widgetKey: String) {
                widgetInfoMap.remove(widgetKey)
            }
    }

    private var widgetKey: String? = null
    private var hostId: Int = 0
    private var currentWidgetId: Int? = null
    private var isRestoredWidget: Boolean = false
    private var isWidgetDeleted: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge()

        // 初始化持久化管理器
        persistenceManager = WidgetPersistenceManager(this)

        var displayId = 0;
        if (Build.VERSION.SDK_INT >= 30) {
            displayId = getDisplay().getDisplayId();
        }
        Log.d(TAG, "onCreate: displayId = $displayId");

        // 获取widget key
        widgetKey = intent.getStringExtra(EXTRA_WIDGET_KEY)
        Log.d(TAG, "onCreate: $widgetKey")

        if (widgetKey == null) {
            finish()
            return
        }

        // 获取widget信息
        val widgetInfo = getWidgetInfo(widgetKey!!)
        Log.d(TAG, "onCreate: $widgetInfo")

        if (widgetInfo == null) {
            finish()
            return
        }

        // 检查是否是恢复的widget
        isRestoredWidget = intent.getBooleanExtra("is_restored_widget", false)
        val oldWidgetIdForDeletion = intent.getIntExtra("old_widget_id_for_deletion", -1)
        val restoredWidth = intent.getIntExtra("restored_width", -1)
        val restoredHeight = intent.getIntExtra("restored_height", -1)
        val skipConfiguration = intent.getBooleanExtra("skip_configuration", false)
        
        // 如果是恢复的widget且有旧的widget ID，先删除旧的widget
        if (isRestoredWidget && oldWidgetIdForDeletion != -1) {
            lifecycleScope.launch {
                try {
                    // 删除旧的widget ID
                    val tempHostManager = WidgetHostManager(this@WidgetDisplayActivity, this@WidgetDisplayActivity, 9999)
                    tempHostManager.initialize()
                    tempHostManager.deleteWidget(oldWidgetIdForDeletion)
                    tempHostManager.destroy()
                    Log.d(TAG, "Deleted old widget ID: $oldWidgetIdForDeletion")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to delete old widget ID: $oldWidgetIdForDeletion", e)
                }
            }
        }

        // 为每个实例生成唯一的hostId，避免多实例冲突
        hostId = getNextHostId()
        Log.d(TAG, "onCreate: hostId = $hostId")

        // 初始化WidgetHostManager，使用唯一的hostId
        widgetHostManager = WidgetHostManager(this, this, hostId)
        widgetHostManager.initialize()

        setContent {
            WidgetManagerTheme {
                WidgetDisplayScreen(
                    widgetInfo = widgetInfo,
                    widgetHostManager = widgetHostManager,
                    onBackClick = {
                        finish()
                    },
                    restoredWidth = if (restoredWidth > 0) restoredWidth else null,
                    restoredHeight = if (restoredHeight > 0) restoredHeight else null,
                    activity = this@WidgetDisplayActivity,
                    skipConfiguration = skipConfiguration
                )
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "onNewIntent: $widgetKey")
    }

    override fun onRestart() {
        super.onRestart()
        Log.d(TAG, "onRestart: $widgetKey")
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume: $widgetKey, hostId: $hostId")
        // 确保WidgetHost在恢复时重新开始监听
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.startListening()
            } catch (e: Exception) {
                Log.e(TAG, "Error starting widget host listening", e)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "onPause: $widgetKey, hostId: $hostId")
        // 暂停时停止监听，但不销毁
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.stopListening()
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping widget host listening", e)
            }
        }
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop: $widgetKey")
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "onConfigurationChanged: $widgetKey")
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy: $widgetKey, hostId: $hostId")

        // 只有在widget被明确删除时才标记为非活跃状态
        // 正常的Activity销毁（如按返回键）不应该影响widget的活跃状态
        if (isWidgetDeleted) {
            widgetKey?.let { key ->
                lifecycleScope.launch {
                    try {
                        persistenceManager.markWidgetInactive(key)
                        Log.d(TAG, "Widget marked as inactive due to deletion: $key")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to mark widget inactive: $key", e)
                    }
                }
            }
        }

        // 销毁WidgetHostManager
        if (::widgetHostManager.isInitialized) {
            try {
                widgetHostManager.destroy()
                Log.d(TAG, "WidgetHostManager destroyed successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error destroying WidgetHostManager", e)
            }
        }

        // 清理对应的widget信息
        widgetKey?.let { key ->
            clearWidgetInfo(key)
            Log.d(TAG, "Widget info cleared for key: $key")
        }
    }

    /**
     * 保存widget信息到数据库
     */
    fun saveWidgetToPersistence(widgetId: Int, width: Int, height: Int, isConfigured: Boolean = false) {
        widgetKey?.let { key ->
            val widgetInfo = getWidgetInfo(key)
            if (widgetInfo != null) {
                currentWidgetId = widgetId
                lifecycleScope.launch {
                    try {
                        persistenceManager.saveWidget(
                            widgetKey = key,
                            widgetInfo = widgetInfo,
                            widgetId = widgetId,
                            isConfigured = isConfigured,
                            width = width,
                            height = height,
                            displayId = if (Build.VERSION.SDK_INT >= 30) display?.displayId ?: 0 else 0
                        )
                        Log.d(TAG, "Widget saved to persistence: $key")
                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to save widget to persistence: $key", e)
                    }
                }
            }
        }
    }

    /**
     * 更新widget尺寸
     */
    fun updateWidgetSize(width: Int, height: Int) {
        widgetKey?.let { key ->
            lifecycleScope.launch {
                try {
                    persistenceManager.updateWidgetSize(key, width, height)
                    Log.d(TAG, "Widget size updated: $key")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to update widget size: $key", e)
                }
            }
        }
    }

    /**
     * 标记widget为已删除
     * 这个方法应该在用户主动删除widget时调用
     */
    fun markWidgetAsDeleted() {
        isWidgetDeleted = true
        Log.d(TAG, "Widget marked as deleted: $widgetKey")
    }
}
