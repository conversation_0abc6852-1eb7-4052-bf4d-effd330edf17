package com.yvr.widgetmanager.viewmodel

import android.appwidget.AppWidgetHostView
import android.content.Context
import androidx.activity.ComponentActivity
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yvr.widgetmanager.WidgetDisplayActivity
import com.yvr.widgetmanager.manager.WidgetHostManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import kotlinx.coroutines.launch

/**
 * Widget显示页面的ViewModel
 */
class WidgetDisplayViewModel : ViewModel() {

    private var widgetHostManager: WidgetHostManager? = null
    private var currentWidgetId: Int? = null
    private var currentWidgetInfo: AppWidgetInfo? = null
    private var activity: WidgetDisplayActivity? = null

    // UI状态
    private val _widgetHostView = mutableStateOf<AppWidgetHostView?>(null)
    val widgetHostView: State<AppWidgetHostView?> = _widgetHostView

    private val _isLoading = mutableStateOf(false)
    val isLoading: State<Boolean> = _isLoading

    private val _error = mutableStateOf<String?>(null)
    val error: State<String?> = _error

    private val _isWidgetBound = mutableStateOf(false)
    val isWidgetBound: State<Boolean> = _isWidgetBound

    // 调整模式状态
    private val _isResizeMode = mutableStateOf(false)
    val isResizeMode: State<Boolean> = _isResizeMode

    // Widget尺寸状态
    private val _widgetWidth = mutableStateOf(300)
    val widgetWidth: State<Int> = _widgetWidth

    private val _widgetHeight = mutableStateOf(200)
    val widgetHeight: State<Int> = _widgetHeight
    
    /**
     * 初始化ViewModel
     */
    fun initialize(
        widgetHostManager: WidgetHostManager,
        widgetInfo: AppWidgetInfo,
        defaultWidth: Int,
        defaultHeight: Int,
        restoredWidth: Int? = null,
        restoredHeight: Int? = null,
        activity: WidgetDisplayActivity? = null,
        skipConfiguration: Boolean = false
    ) {
        // 重置状态
        resetState()

        this.widgetHostManager = widgetHostManager
        this.currentWidgetInfo = widgetInfo
        this.activity = activity

        // 设置widget尺寸：优先使用恢复的尺寸，否则使用默认尺寸
        _widgetWidth.value = restoredWidth ?: defaultWidth
        _widgetHeight.value = restoredHeight ?: defaultHeight

        bindWidget(widgetInfo, skipConfiguration)
    }

    /**
     * 重置状态
     */
    private fun resetState() {
        _widgetHostView.value = null
        _isLoading.value = false
        _error.value = null
        _isWidgetBound.value = false
        _isResizeMode.value = false
        currentWidgetId = null
    }
    
    /**
     * 绑定Widget
     */
    private fun bindWidget(widgetInfo: AppWidgetInfo, skipConfiguration: Boolean = false) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            widgetHostManager?.bindAndDisplayWidget(
                widgetInfo,
                object : WidgetHostManager.WidgetBindCallback {
                    override fun onWidgetBound(widgetId: Int, hostView: AppWidgetHostView) {
                        currentWidgetId = widgetId
                        _widgetHostView.value = hostView
                        _isWidgetBound.value = true
                        _isLoading.value = false

                        // 保存widget到持久化存储
                        activity?.saveWidgetToPersistence(
                            widgetId = widgetId,
                            width = _widgetWidth.value,
                            height = _widgetHeight.value,
                            isConfigured = false
                        )
                    }

                    override fun onWidgetBindFailed(error: String) {
                        _error.value = error
                        _isLoading.value = false
                        _isWidgetBound.value = false
                    }

                    override fun onWidgetConfigured(widgetId: Int, hostView: AppWidgetHostView) {
                        currentWidgetId = widgetId
                        _widgetHostView.value = hostView
                        _isWidgetBound.value = true
                        _isLoading.value = false

                        // 保存已配置的widget到持久化存储
                        activity?.saveWidgetToPersistence(
                            widgetId = widgetId,
                            width = _widgetWidth.value,
                            height = _widgetHeight.value,
                            isConfigured = true
                        )
                    }
                },
                skipConfiguration
            )
        }
    }
    
    /**
     * 重试绑定
     */
    fun retryBinding() {
        currentWidgetInfo?.let { widgetInfo ->
            resetState()
            bindWidget(widgetInfo)
        }
    }
    
    /**
     * 移除Widget
     */
    fun removeWidget() {
        currentWidgetId?.let { widgetId ->
            widgetHostManager?.deleteWidget(widgetId)
            resetState()
        }
    }
    
    /**
     * 进入调整模式
     */
    fun enterResizeMode() {
        _isResizeMode.value = true
    }

    /**
     * 退出调整模式
     */
    fun exitResizeMode() {
        _isResizeMode.value = false
    }

    /**
     * 更新Widget尺寸
     */
    fun updateWidgetSize(width: Int, height: Int) {
        _widgetWidth.value = width
        _widgetHeight.value = height

        // 更新持久化存储中的尺寸
        activity?.updateWidgetSize(width, height)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 只重置状态，不销毁WidgetHostManager（由MainActivity管理）
        resetState()
        widgetHostManager = null
        currentWidgetInfo = null
    }
    
    override fun onCleared() {
        super.onCleared()
        cleanup()
    }
}
