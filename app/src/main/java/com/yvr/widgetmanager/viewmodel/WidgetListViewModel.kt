package com.yvr.widgetmanager.viewmodel

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.yvr.widgetmanager.manager.WidgetManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import com.yvr.widgetmanager.model.SortOrder
import com.yvr.widgetmanager.model.SortType
import com.yvr.widgetmanager.model.WidgetGroup
import com.yvr.widgetmanager.WidgetDisplayActivity
import com.yvr.widgetmanager.WidgetDisplayActivity.Companion.EXTRA_WIDGET_KEY
import com.yvr.widgetmanager.WidgetDisplayActivity.Companion.widgetInfoMap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Widget列表的ViewModel
 */
class WidgetListViewModel : ViewModel() {

    private var TAG = "WidgetListViewModel";

    private lateinit var widgetManager: WidgetManager
    private var cachedGroups: List<WidgetGroup>? = null
    private var lastSortType: SortType = SortType.INSTALL_TIME
    private var lastSortOrder: SortOrder = SortOrder.DESCENDING
    
    // UI状态
    private val _widgetGroups = mutableStateOf<List<WidgetGroup>>(emptyList())
    val widgetGroups: State<List<WidgetGroup>> = _widgetGroups
    
    private val _isLoading = mutableStateOf(false)
    val isLoading: State<Boolean> = _isLoading
    
    private val _selectedWidget = mutableStateOf<AppWidgetInfo?>(null)
    val selectedWidget: State<AppWidgetInfo?> = _selectedWidget
    
    private val _sortType = mutableStateOf(SortType.INSTALL_TIME)
    val sortType: State<SortType> = _sortType
    
    private val _sortOrder = mutableStateOf(SortOrder.DESCENDING)
    val sortOrder: State<SortOrder> = _sortOrder
    
    private val _showSortDialog = mutableStateOf(false)
    val showSortDialog: State<Boolean> = _showSortDialog
    
    private val _error = mutableStateOf<String?>(null)
    val error: State<String?> = _error
    
    /**
     * 初始化ViewModel
     */
    fun initialize(context: Context) {
        widgetManager = WidgetManager(context)
        loadWidgets()
    }
    
    /**
     * 加载所有widget
     */
    fun loadWidgets(forceReload: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                _isLoading.value = true
                _error.value = null
                val needReload = forceReload || cachedGroups == null || _sortType.value != lastSortType || _sortOrder.value != lastSortOrder
                if (needReload) {
                    val groups = widgetManager.getAllWidgetGroups(_sortType.value, _sortOrder.value)
                    cachedGroups = groups
                    lastSortType = _sortType.value
                    lastSortOrder = _sortOrder.value
                }
                _widgetGroups.value = cachedGroups ?: emptyList()
            } catch (e: Exception) {
                _error.value = "加载Widget失败: ${e.message}"
                e.printStackTrace()
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 选择一个widget
     */
    fun selectWidget(widget: AppWidgetInfo?) {
        _selectedWidget.value = widget
    }
    
    /**
     * 更新排序设置
     */
    fun updateSort(sortType: SortType, sortOrder: SortOrder) {
        _sortType.value = sortType
        _sortOrder.value = sortOrder
        loadWidgets(forceReload = true)
    }
    
    /**
     * 显示排序对话框
     */
    fun showSortDialog() {
        _showSortDialog.value = true
    }
    
    /**
     * 隐藏排序对话框
     */
    fun hideSortDialog() {
        _showSortDialog.value = false
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        loadWidgets(forceReload = true)
    }

    /**
     * 导航到Widget显示页面
     */
    fun navigateToWidgetDisplay(context: Context, widget: AppWidgetInfo) {
        Log.d(TAG, "navigateToWidgetDisplay: $widget")

        val widgetKey = "${widget.packageName}_${widget.className}_${System.currentTimeMillis()}_${java.util.UUID.randomUUID().toString().substring(0, 8)}"
        widgetInfoMap[widgetKey] = widget
        // 创建 Intent 并添加配置
        val intent = Intent(context, WidgetDisplayActivity::class.java).apply {
            putExtra(EXTRA_WIDGET_KEY, widgetKey)
            putExtra("new_display_each", true)
            addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        // 启动 WidgetDisplayActivity
        Log.d(TAG, "startActivity: $widgetKey");
        context.startActivity(intent)
    }
}
