package com.yvr.widgetmanager

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.yvr.widgetmanager.manager.WidgetHostManager
import com.yvr.widgetmanager.navigation.WidgetNavigation
import com.yvr.widgetmanager.service.WidgetRestoreService
import com.yvr.widgetmanager.ui.theme.WidgetManagerTheme

class MainActivity : ComponentActivity() {

    private lateinit var widgetHostManager: WidgetHostManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 启动Widget恢复服务
        WidgetRestoreService.startRestore(this)

        // 初始化WidgetHostManager
        widgetHostManager = WidgetHostManager(this, this)
        widgetHostManager.initialize()

        setContent {
            WidgetManagerTheme {
                WidgetNavigation(widgetHostManager = widgetHostManager)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        widgetHostManager.destroy()
    }
}