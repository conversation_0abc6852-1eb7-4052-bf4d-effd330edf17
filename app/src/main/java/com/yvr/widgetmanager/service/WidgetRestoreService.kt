package com.yvr.widgetmanager.service

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.IBinder
import android.util.Log
import com.yvr.widgetmanager.WidgetDisplayActivity
import com.yvr.widgetmanager.data.WidgetRestoreInfo
import com.yvr.widgetmanager.manager.WidgetManager
import com.yvr.widgetmanager.manager.WidgetPersistenceManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import kotlinx.coroutines.*

/**
 * Widget恢复服务
 * 负责在应用重启后自动恢复之前创建的Widget
 */
class WidgetRestoreService : Service() {
    
    companion object {
        private const val TAG = "WidgetRestoreService"
        private const val ACTION_RESTORE_WIDGETS = "com.yvr.widgetmanager.RESTORE_WIDGETS"
        private const val RESTORE_DELAY_MS = 3000L // 延迟3秒开始恢复，确保系统稳定
        
        /**
         * 启动Widget恢复服务
         */
        fun startRestore(context: Context) {
            val intent = Intent(context, WidgetRestoreService::class.java).apply {
                action = ACTION_RESTORE_WIDGETS
            }
            context.startService(intent)
        }
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.Main + Job())
    private lateinit var persistenceManager: WidgetPersistenceManager
    private lateinit var widgetManager: WidgetManager
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "WidgetRestoreService created")
        persistenceManager = WidgetPersistenceManager(this)
        widgetManager = WidgetManager(this)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "WidgetRestoreService started")
        
        when (intent?.action) {
            ACTION_RESTORE_WIDGETS -> {
                startWidgetRestore()
            }
        }
        
        return START_NOT_STICKY // 完成任务后不需要重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    /**
     * 开始Widget恢复流程
     */
    private fun startWidgetRestore() {
        serviceScope.launch {
            try {
                Log.d(TAG, "Starting widget restore process...")
                
                // 延迟一段时间，确保系统稳定
                delay(RESTORE_DELAY_MS)
                
                // 清理过期的Widget数据
                persistenceManager.cleanupExpiredWidgets()
                
                // 获取需要恢复的Widget列表
                val widgetsToRestore = persistenceManager.getWidgetsToRestore()
                Log.d(TAG, "Found ${widgetsToRestore.size} widgets to restore")
                
                if (widgetsToRestore.isEmpty()) {
                    Log.d(TAG, "No widgets to restore")
                    stopSelf()
                    return@launch
                }
                
                // 逐个恢复Widget
                restoreWidgets(widgetsToRestore)
                
                Log.d(TAG, "Widget restore process completed")
                stopSelf()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error during widget restore", e)
                stopSelf()
            }
        }
    }
    
    /**
     * 恢复Widget列表
     */
    private suspend fun restoreWidgets(widgetsToRestore: List<WidgetRestoreInfo>) {
        for ((index, restoreInfo) in widgetsToRestore.withIndex()) {
            try {
                Log.d(TAG, "Restoring widget ${index + 1}/${widgetsToRestore.size}: ${restoreInfo.entity.label}")

                // 检查widget ID是否仍然有效
                if (!isWidgetIdValid(restoreInfo.entity.widgetId)) {
                    Log.w(TAG, "Widget ID no longer valid: ${restoreInfo.entity.widgetId}")
                    // 标记为非活跃状态
                    persistenceManager.markWidgetInactive(restoreInfo.entity.id)
                    continue
                }

                // 获取Widget信息
                val widgetInfo = getWidgetInfoFromEntity(restoreInfo.entity)
                if (widgetInfo == null) {
                    Log.w(TAG, "Widget provider not found: ${restoreInfo.entity.packageName}/${restoreInfo.entity.className}")
                    // 标记为非活跃状态
                    persistenceManager.markWidgetInactive(restoreInfo.entity.id)
                    continue
                }

                // 恢复Widget
                restoreWidget(restoreInfo, widgetInfo)

                // 添加延迟，避免同时创建太多Widget
                delay(2000)

            } catch (e: Exception) {
                Log.e(TAG, "Failed to restore widget: ${restoreInfo.entity.label}", e)
                // 标记为非活跃状态
                persistenceManager.markWidgetInactive(restoreInfo.entity.id)
            }
        }
    }
    
    /**
     * 恢复单个Widget
     */
    private suspend fun restoreWidget(restoreInfo: WidgetRestoreInfo, widgetInfo: AppWidgetInfo) {
        val entity = restoreInfo.entity
        
        try {
            // 创建新的Widget Key
            val newWidgetKey = "${entity.packageName}_${entity.className}_${System.currentTimeMillis()}_restored"
            
            // 将Widget信息添加到全局Map中
            WidgetDisplayActivity.widgetInfoMap[newWidgetKey] = widgetInfo
            
            // 获取保存的配置信息
            val savedConfiguration = persistenceManager.getWidgetConfiguration(entity.id)

            // 创建启动Intent
            val intent = Intent(this, WidgetDisplayActivity::class.java).apply {
                putExtra(WidgetDisplayActivity.EXTRA_WIDGET_KEY, newWidgetKey)
                putExtra("is_restored_widget", true)
                putExtra("old_widget_id_for_deletion", entity.widgetId)
                putExtra("restored_width", entity.width)
                putExtra("restored_height", entity.height)
                
                if (entity.isConfigured) {
                    // 如果之前已配置，跳过配置流程
                    putExtra("skip_configuration", true)
                    savedConfiguration?.let { config ->
                        putExtra("saved_configuration", entity.configurationData)
                    }
                }

                putExtra("new_display_each", true)
                addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            // 启动WidgetDisplayActivity
            startActivity(intent)

            Log.d(TAG, "Started WidgetDisplayActivity for widget: ${entity.label}")

            // 延迟一下再更新数据库，确保Activity已经启动
            delay(500)

            // 更新数据库中的Widget Key
            persistenceManager.deleteWidget(entity.id)
            persistenceManager.saveWidget(
                widgetKey = newWidgetKey,
                widgetInfo = widgetInfo,
                widgetId = entity.widgetId,
                isConfigured = entity.isConfigured,
                configurationData = savedConfiguration,
                width = entity.width,
                height = entity.height,
                displayId = entity.displayId
            )
            
            Log.d(TAG, "Widget restored successfully: ${entity.label}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to restore widget: ${entity.label}", e)
            throw e
        }
    }
    
    /**
     * 从实体获取Widget信息
     */
    private suspend fun getWidgetInfoFromEntity(entity: com.yvr.widgetmanager.data.WidgetEntity): AppWidgetInfo? {
        return try {
            // 获取所有Widget分组，然后展平为Widget列表
            val allWidgetGroups = widgetManager.getAllWidgetGroups(
                com.yvr.widgetmanager.model.SortType.ALPHABETICAL,
                com.yvr.widgetmanager.model.SortOrder.ASCENDING
            )
            val allWidgets = allWidgetGroups.flatMap { it.widgets }

            allWidgets.find {
                it.packageName == entity.packageName &&
                it.className == entity.className
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get widget info for: ${entity.packageName}/${entity.className}", e)
            null
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "WidgetRestoreService destroyed")
    }
}
