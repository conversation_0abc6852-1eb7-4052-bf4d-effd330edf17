package com.yvr.widgetmanager

import android.appwidget.AppWidgetHost
import android.appwidget.AppWidgetManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityOptionsCompat
import com.yvr.widgetmanager.model.AppWidgetInfo
import com.yvr.widgetmanager.viewmodel.WidgetListViewModel
import java.util.concurrent.ConcurrentHashMap

/**
 * Widget配置Activity
 * 专门用于处理需要配置的Widget
 */
class WidgetConfigActivity : ComponentActivity() {
    
    companion object {
        private const val TAG = "WidgetConfigActivity"
        private const val EXTRA_WIDGET_KEY = "widget_key"
        
        // 使用Map存储widget信息
        private val widgetInfoMap = ConcurrentHashMap<String, AppWidgetInfo>()

        // 为每个实例生成唯一的HOST_ID
        private var nextHostId = 2000
        private fun getNextHostId(): Int {
            return synchronized(this) {
                nextHostId++
            }
        }

        /**
         * 启动Widget配置Activity
         */
        fun start(context: Context, widgetInfo: AppWidgetInfo) {
            val widgetKey = "${widgetInfo.packageName}_${widgetInfo.className}_${System.currentTimeMillis()}"
            widgetInfoMap[widgetKey] = widgetInfo
            
            val intent = Intent(context, WidgetConfigActivity::class.java).apply {
                putExtra(EXTRA_WIDGET_KEY, widgetKey)
                putExtra("new_display_each", true)
                addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
        
        /**
         * 获取Widget信息
         */
        private fun getWidgetInfo(widgetKey: String): AppWidgetInfo? {
            return widgetInfoMap[widgetKey]
        }
        
        /**
         * 清理Widget信息
         */
        private fun clearWidgetInfo(widgetKey: String) {
            widgetInfoMap.remove(widgetKey)
        }
    }
    
    private var widgetKey: String? = null
    private lateinit var configLauncher: ActivityResultLauncher<Intent>
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取widget key
        widgetKey = intent.getStringExtra(EXTRA_WIDGET_KEY)
        Log.d(TAG, "onCreate: $widgetKey")
        
        if (widgetKey == null) {
            finish()
            return
        }
        
        // 获取widget信息
        val widgetInfo = getWidgetInfo(widgetKey!!)
        if (widgetInfo == null) {
            Log.e(TAG, "Widget info not found for key: $widgetKey")
            finish()
            return
        }
        
        // 注册配置结果处理器
        configLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleConfigResult(result.resultCode, result.data, widgetInfo)
        }
        
        // 启动Widget配置
        startWidgetConfiguration(widgetInfo)
    }
    
    /**
     * 启动Widget配置
     */
    private fun startWidgetConfiguration(widgetInfo: AppWidgetInfo) {
        val providerInfo = widgetInfo.providerInfo
        
        if (providerInfo.configure == null) {
            Log.e(TAG, "Widget does not require configuration")
            finish()
            return
        }
        
        // 分配Widget ID
        val appWidgetManager = AppWidgetManager.getInstance(this)
        val appWidgetHost = AppWidgetHost(this, getNextHostId())
        val widgetId = appWidgetHost.allocateAppWidgetId()
        
        // 绑定Widget
        val canBind = appWidgetManager.bindAppWidgetIdIfAllowed(
            widgetId,
            providerInfo.provider
        )
        
        if (!canBind) {
            Log.e(TAG, "Cannot bind widget")
            appWidgetHost.deleteAppWidgetId(widgetId)
            finish()
            return
        }
        
        // 启动配置Intent
        val configIntent = Intent(AppWidgetManager.ACTION_APPWIDGET_CONFIGURE).apply {
            component = providerInfo.configure
            putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, widgetId)
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                var displayId = getDisplay().getDisplayId();
                Log.d(TAG, "startWidgetConfiguration: displayId = $displayId")
                val activityOptions = ActivityOptionsCompat.makeBasic()
                activityOptions.setLaunchDisplayId(displayId)
                configLauncher?.launch(configIntent, activityOptions)
            } else {
                configLauncher?.launch(configIntent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start configuration", e)
            appWidgetHost.deleteAppWidgetId(widgetId)
            finish()
        }
    }
    
    /**
     * 处理配置结果
     */
    private fun handleConfigResult(resultCode: Int, data: Intent?, widgetInfo: AppWidgetInfo) {

        Handler(Looper.getMainLooper()).postDelayed({
            // 延迟1秒，等待配置Activity关闭

            if (resultCode == RESULT_OK) {
                val widgetId = data?.getIntExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, -1) ?: -1
                if (widgetId != -1) {
                    Log.d(TAG, "Widget configured successfully, widgetId: $widgetId")
                    // 配置成功，启动WidgetDisplayActivity
                    val newWidgetKey = "${widgetInfo.packageName}_${widgetInfo.className}_${System.currentTimeMillis()}_configured"
                    WidgetDisplayActivity.widgetInfoMap[newWidgetKey] = widgetInfo
                    val activityOptions = ActivityOptionsCompat.makeBasic()
                    val intent = Intent(this, WidgetDisplayActivity::class.java).apply {
                        putExtra(WidgetDisplayActivity.EXTRA_WIDGET_KEY, newWidgetKey)
                        putExtra("configured_widget_id", widgetId)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                            var displayId = getDisplay().getDisplayId();
                            Log.d("WidgetHostManager", "startWidgetConfiguration: displayId = $displayId")
                            activityOptions.setLaunchDisplayId(displayId)
                        }
                        addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    startActivity(intent, activityOptions.toBundle())
                } else {
                    Log.e(TAG, "Invalid widget ID after configuration")
                }
            } else {
                Log.d(TAG, "Widget configuration cancelled")
            }

            // 清理并关闭
            widgetKey?.let { clearWidgetInfo(it) }
            finish()

        }, 100)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy: $widgetKey")
        widgetKey?.let { clearWidgetInfo(it) }
    }
}
