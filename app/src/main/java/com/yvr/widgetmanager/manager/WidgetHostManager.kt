package com.yvr.widgetmanager.manager

import android.app.Activity
import android.app.ActivityOptions
import android.appwidget.AppWidgetHost
import android.appwidget.AppWidgetHostView
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProviderInfo
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.os.Build
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.ViewCompat.getDisplay
import com.yvr.widgetmanager.model.AppWidgetInfo
import androidx.core.app.ActivityOptionsCompat

/**
 * Widget主机管理器，负责管理widget的绑定和显示
 */
class WidgetHostManager(
    private val context: Context,
    private val activity: ComponentActivity,
    private val hostId: Int = 1024 // 支持自定义hostId，默认为1024
) {
    companion object {
        private const val REQUEST_BIND_APPWIDGET = 1001
        private const val REQUEST_CONFIGURE_APPWIDGET = 1002
    }

    private val appWidgetManager = AppWidgetManager.getInstance(context)
    private val appWidgetHost = AppWidgetHost(context, hostId)

    // 存储已分配的widget ID
    private val allocatedWidgetIds = mutableSetOf<Int>()

    // 当前绑定的widget信息
    private var currentWidgetId: Int? = null
    private var currentHostView: AppWidgetHostView? = null
    
    // 回调接口
    interface WidgetBindCallback {
        fun onWidgetBound(widgetId: Int, hostView: AppWidgetHostView)
        fun onWidgetBindFailed(error: String)
        fun onWidgetConfigured(widgetId: Int, hostView: AppWidgetHostView)
    }
    
    private var bindCallback: WidgetBindCallback? = null
    
    // Activity Result Launchers
    private var bindWidgetLauncher: ActivityResultLauncher<Intent>? = null
    private var configureWidgetLauncher: ActivityResultLauncher<Intent>? = null
    
    /**
     * 初始化Widget主机
     */
    fun initialize() {
        appWidgetHost.startListening()
        
        // 注册Activity Result Launchers
        bindWidgetLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleBindWidgetResult(result.resultCode, result.data)
        }
        
        configureWidgetLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            handleConfigureWidgetResult(result.resultCode, result.data)
        }
    }
    
    /**
     * 开始监听Widget事件
     */
    fun startListening() {
        try {
            appWidgetHost.startListening()
        } catch (e: Exception) {
            // 忽略启动监听失败的情况
        }
    }

    /**
     * 停止监听Widget事件
     */
    fun stopListening() {
        try {
            appWidgetHost.stopListening()
        } catch (e: Exception) {
            // 忽略停止监听失败的情况
        }
    }

    /**
     * 销毁Widget主机
     */
    fun destroy() {
        try {
            // 清理当前widget
            clearCurrentWidget()

            // 停止监听
            appWidgetHost.stopListening()

            // 删除所有已分配的widget
            allocatedWidgetIds.forEach { widgetId ->
                try {
                    appWidgetHost.deleteAppWidgetId(widgetId)
                } catch (e: Exception) {
                    // 忽略删除失败的情况
                }
            }
            allocatedWidgetIds.clear()
        } catch (e: Exception) {
            // 忽略销毁过程中的异常
        }
    }

    /**
     * 清理当前widget
     */
    private fun clearCurrentWidget() {
        currentWidgetId?.let { widgetId ->
            try {
                appWidgetHost.deleteAppWidgetId(widgetId)
                allocatedWidgetIds.remove(widgetId)
            } catch (e: Exception) {
                // 忽略删除失败
            }
        }
        currentWidgetId = null
        currentHostView = null
    }
    
    /**
     * 绑定并显示widget
     */
    fun bindAndDisplayWidget(
        widgetInfo: AppWidgetInfo,
        callback: WidgetBindCallback
    ) {
        this.bindCallback = callback

        try {
            // 清理之前的widget
            clearCurrentWidget()

            // 分配新的widget ID
            val widgetId = appWidgetHost.allocateAppWidgetId()
            allocatedWidgetIds.add(widgetId)
            currentWidgetId = widgetId

            val providerInfo = widgetInfo.providerInfo

            // 检查是否需要权限绑定
            val canBind = appWidgetManager.bindAppWidgetIdIfAllowed(
                widgetId,
                providerInfo.provider
            )

            if (canBind) {
                // 直接绑定成功，检查是否需要配置
                if (providerInfo.configure != null) {
                    // 需要配置
                    startWidgetConfiguration(widgetId, providerInfo)
                } else {
                    // 不需要配置，直接创建HostView
                    createHostView(widgetId, providerInfo)
                }
            } else {
                // 需要用户授权绑定
                requestBindWidget(widgetId, providerInfo)
            }

        } catch (e: Exception) {
            callback.onWidgetBindFailed("绑定Widget失败: ${e.message}")
        }
    }
    
    /**
     * 请求绑定widget权限
     */
    private fun requestBindWidget(widgetId: Int, providerInfo: AppWidgetProviderInfo) {
        val intent = Intent(AppWidgetManager.ACTION_APPWIDGET_BIND).apply {
            putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, widgetId)
            putExtra(AppWidgetManager.EXTRA_APPWIDGET_PROVIDER, providerInfo.provider)
        }
        
        bindWidgetLauncher?.launch(intent)
    }
    
    /**
     * 启动widget配置
     */
    private fun startWidgetConfiguration(widgetId: Int, providerInfo: AppWidgetProviderInfo) {
        val configureIntent = Intent(AppWidgetManager.ACTION_APPWIDGET_CONFIGURE).apply {
            component = providerInfo.configure
            putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, widgetId)
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val displayId = activity.display?.displayId ?: android.view.Display.DEFAULT_DISPLAY
            Log.d("WidgetHostManager", "startWidgetConfiguration: displayId = $displayId")
            val activityOptions = ActivityOptionsCompat.makeBasic()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                activityOptions.setLaunchDisplayId(displayId)
            }
            configureWidgetLauncher?.launch(configureIntent, activityOptions)
        } else {
            configureWidgetLauncher?.launch(configureIntent)
        }
    }
    
    /**
     * 创建HostView
     */
    private fun createHostView(widgetId: Int, providerInfo: AppWidgetProviderInfo) {
        try {
            val hostView = appWidgetHost.createView(context, widgetId, providerInfo)
            currentHostView = hostView
            bindCallback?.onWidgetBound(widgetId, hostView)
        } catch (e: Exception) {
            bindCallback?.onWidgetBindFailed("创建Widget视图失败: ${e.message}")
        }
    }
    
    /**
     * 处理绑定widget的结果
     */
    private fun handleBindWidgetResult(resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            val widgetId = data?.getIntExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, -1) ?: -1
            if (widgetId != -1) {
                val providerInfo = appWidgetManager.getAppWidgetInfo(widgetId)
                if (providerInfo != null) {
                    if (providerInfo.configure != null) {
                        startWidgetConfiguration(widgetId, providerInfo)
                    } else {
                        createHostView(widgetId, providerInfo)
                    }
                } else {
                    bindCallback?.onWidgetBindFailed("无法获取Widget信息")
                }
            } else {
                bindCallback?.onWidgetBindFailed("无效的Widget ID")
            }
        } else {
            bindCallback?.onWidgetBindFailed("用户取消了Widget绑定")
        }
    }
    
    /**
     * 处理配置widget的结果
     */
    private fun handleConfigureWidgetResult(resultCode: Int, data: Intent?) {
        if (resultCode == Activity.RESULT_OK) {
            val widgetId = data?.getIntExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, -1) ?: -1
            if (widgetId != -1) {
                val providerInfo = appWidgetManager.getAppWidgetInfo(widgetId)
                if (providerInfo != null) {
                    val hostView = appWidgetHost.createView(context, widgetId, providerInfo)
                    currentHostView = hostView
                    bindCallback?.onWidgetConfigured(widgetId, hostView)
                } else {
                    bindCallback?.onWidgetBindFailed("配置完成后无法获取Widget信息")
                }
            } else {
                bindCallback?.onWidgetBindFailed("配置完成后无效的Widget ID")
            }
        } else {
            bindCallback?.onWidgetBindFailed("用户取消了Widget配置")
        }
    }
    
    /**
     * 删除widget
     */
    fun deleteWidget(widgetId: Int) {
        try {
            appWidgetHost.deleteAppWidgetId(widgetId)
            allocatedWidgetIds.remove(widgetId)

            if (currentWidgetId == widgetId) {
                currentWidgetId = null
                currentHostView = null
            }
        } catch (e: Exception) {
            // 忽略删除失败
        }
    }

    /**
     * 获取当前的HostView
     */
    fun getCurrentHostView(): AppWidgetHostView? = currentHostView
}
