package com.yvr.widgetmanager.manager

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.yvr.widgetmanager.data.WidgetConfiguration
import com.yvr.widgetmanager.data.WidgetDao
import com.yvr.widgetmanager.data.WidgetDatabase
import com.yvr.widgetmanager.data.WidgetEntity
import com.yvr.widgetmanager.data.WidgetRestoreInfo
import com.yvr.widgetmanager.model.AppWidgetInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Widget持久化管理器
 * 负责Widget信息的保存、恢复和管理
 */
class WidgetPersistenceManager(private val context: Context) {
    
    companion object {
        private const val TAG = "WidgetPersistenceManager"
        private const val CLEANUP_EXPIRE_DAYS = 7L
    }
    
    private val database = WidgetDatabase.getDatabase(context)
    private val widgetDao: WidgetDao = database.widgetDao()
    private val gson = Gson()
    
    /**
     * 保存Widget信息
     */
    suspend fun saveWidget(
        widgetKey: String,
        widgetInfo: AppWidgetInfo,
        widgetId: Int,
        isConfigured: Boolean = false,
        configurationData: WidgetConfiguration? = null,
        width: Int = widgetInfo.minWidth,
        height: Int = widgetInfo.minHeight,
        displayId: Int = 0
    ) {
        withContext(Dispatchers.IO) {
            try {
                val configDataJson = configurationData?.let { 
                    gson.toJson(it) 
                }
                
                val entity = WidgetEntity(
                    id = widgetKey,
                    packageName = widgetInfo.packageName,
                    className = widgetInfo.className,
                    label = widgetInfo.label,
                    widgetId = widgetId,
                    isConfigured = isConfigured,
                    configurationData = configDataJson,
                    width = width,
                    height = height,
                    displayId = displayId,
                    createdTime = System.currentTimeMillis(),
                    lastActiveTime = System.currentTimeMillis(),
                    isActive = true
                )
                
                widgetDao.insertOrUpdateWidget(entity)
                Log.d(TAG, "Widget saved: $widgetKey")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save widget: $widgetKey", e)
            }
        }
    }
    
    /**
     * 更新Widget配置信息
     */
    suspend fun updateWidgetConfiguration(
        widgetKey: String,
        isConfigured: Boolean,
        configurationData: WidgetConfiguration?
    ) {
        withContext(Dispatchers.IO) {
            try {
                val configDataJson = configurationData?.let { 
                    gson.toJson(it) 
                }
                widgetDao.updateWidgetConfiguration(widgetKey, isConfigured, configDataJson)
                Log.d(TAG, "Widget configuration updated: $widgetKey")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update widget configuration: $widgetKey", e)
            }
        }
    }
    
    /**
     * 更新Widget尺寸
     */
    suspend fun updateWidgetSize(widgetKey: String, width: Int, height: Int) {
        withContext(Dispatchers.IO) {
            try {
                widgetDao.updateWidgetSize(widgetKey, width, height)
                Log.d(TAG, "Widget size updated: $widgetKey")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update widget size: $widgetKey", e)
            }
        }
    }
    
    /**
     * 标记Widget为非活跃状态（销毁时调用）
     */
    suspend fun markWidgetInactive(widgetKey: String) {
        withContext(Dispatchers.IO) {
            try {
                widgetDao.updateWidgetActiveStatus(
                    widgetKey, 
                    false, 
                    System.currentTimeMillis()
                )
                Log.d(TAG, "Widget marked as inactive: $widgetKey")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to mark widget inactive: $widgetKey", e)
            }
        }
    }
    
    /**
     * 删除Widget信息
     */
    suspend fun deleteWidget(widgetKey: String) {
        withContext(Dispatchers.IO) {
            try {
                widgetDao.deleteWidgetById(widgetKey)
                Log.d(TAG, "Widget deleted: $widgetKey")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to delete widget: $widgetKey", e)
            }
        }
    }
    
    /**
     * 获取需要恢复的Widget列表
     */
    suspend fun getWidgetsToRestore(): List<WidgetRestoreInfo> {
        return withContext(Dispatchers.IO) {
            try {
                val entities = widgetDao.getWidgetsToRestore()
                entities.map { entity ->
                    WidgetRestoreInfo(
                        entity = entity,
                        needsRecreation = true,
                        skipConfiguration = entity.isConfigured
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get widgets to restore", e)
                emptyList()
            }
        }
    }
    
    /**
     * 获取Widget配置信息
     */
    suspend fun getWidgetConfiguration(widgetKey: String): WidgetConfiguration? {
        return withContext(Dispatchers.IO) {
            try {
                val entity = widgetDao.getWidgetById(widgetKey)
                entity?.configurationData?.let { configJson ->
                    val type = object : TypeToken<WidgetConfiguration>() {}.type
                    gson.fromJson<WidgetConfiguration>(configJson, type)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get widget configuration: $widgetKey", e)
                null
            }
        }
    }
    
    /**
     * 清理过期的Widget数据
     */
    suspend fun cleanupExpiredWidgets() {
        withContext(Dispatchers.IO) {
            try {
                val expireTime = System.currentTimeMillis() - (CLEANUP_EXPIRE_DAYS * 24 * 60 * 60 * 1000)
                widgetDao.cleanupExpiredWidgets(expireTime)
                Log.d(TAG, "Expired widgets cleaned up")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cleanup expired widgets", e)
            }
        }
    }

    /**
     * 清理无效的Widget（widget ID不再有效的）
     */
    suspend fun cleanupInvalidWidgets() {
        withContext(Dispatchers.IO) {
            try {
                val appWidgetManager = android.appwidget.AppWidgetManager.getInstance(context)
                val activeWidgets = widgetDao.getAllActiveWidgets()

                for (widget in activeWidgets) {
                    try {
                        val widgetInfo = appWidgetManager.getAppWidgetInfo(widget.widgetId)
                        if (widgetInfo == null) {
                            // Widget ID无效，标记为非活跃
                            widgetDao.updateWidgetActiveStatus(
                                widget.id,
                                false,
                                System.currentTimeMillis()
                            )
                            Log.d(TAG, "Marked invalid widget as inactive: ${widget.id}")
                        }
                    } catch (e: Exception) {
                        // 如果检查失败，也标记为非活跃
                        widgetDao.updateWidgetActiveStatus(
                            widget.id,
                            false,
                            System.currentTimeMillis()
                        )
                        Log.w(TAG, "Marked problematic widget as inactive: ${widget.id}", e)
                    }
                }

                Log.d(TAG, "Invalid widgets cleanup completed")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to cleanup invalid widgets", e)
            }
        }
    }
    
    /**
     * 获取所有活跃的Widget
     */
    suspend fun getAllActiveWidgets(): List<WidgetEntity> {
        return withContext(Dispatchers.IO) {
            try {
                widgetDao.getAllActiveWidgets()
            } catch (e: Exception) {
                Log.e(TAG, "Failed to get active widgets", e)
                emptyList()
            }
        }
    }
}
