package com.yvr.widgetmanager.ui.components

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.pointer.PointerInputScope
import kotlinx.coroutines.delay
import kotlin.math.sqrt

/**
 * 自定义长按手势检测，避免长按松手时触发点击事件
 * @param longPressTimeoutMillis 长按触发时间（毫秒）
 * @param onLongPress 长按回调
 * @param onTap 点击回调
 * @param onDoubleTap 双击回调
 */
suspend fun PointerInputScope.detectCustomTapGestures(
    longPressTimeoutMillis: Long = 1000L, // 1秒长按时间，避免误触
    onLongPress: ((Offset) -> Unit)? = null,
    onTap: ((Offset) -> Unit)? = null,
    onDoubleTap: ((Offset) -> Unit)? = null
) {
    var lastTapTime = 0L
    var lastTapPosition = Offset.Zero
    val doubleTapTimeoutMillis = 300L // 双击间隔时间
    val maxDoubleTapDistance = 100f // 双击最大距离

    detectTapGestures(
        onLongPress = { offset ->
            onLongPress?.invoke(offset)
            lastTapTime = 0L // 重置双击状态
        },
        onTap = { offset ->
            val currentTime = System.currentTimeMillis()

            // 检查是否是双击
            val timeSinceLastTap = currentTime - lastTapTime
            val distanceFromLastTap = getDistance(offset, lastTapPosition)

            if (onDoubleTap != null &&
                lastTapTime > 0 &&
                timeSinceLastTap <= doubleTapTimeoutMillis &&
                distanceFromLastTap <= maxDoubleTapDistance) {
                // 双击
                onDoubleTap.invoke(offset)
                lastTapTime = 0L // 重置，避免三击
            } else {
                // 记录这次点击，等待可能的第二次点击
                lastTapTime = currentTime
                lastTapPosition = offset

                if (onDoubleTap == null) {
                    // 没有双击处理，直接执行单击
                    onTap?.invoke(offset)
                }
                // 如果有双击处理，单击会在延迟后执行（如果没有第二次点击的话）
                // 这个逻辑需要在调用方处理
            }
        }
    )
}

/**
 * 计算两点间距离
 */
private fun getDistance(point1: Offset, point2: Offset): Float {
    val dx = point1.x - point2.x
    val dy = point1.y - point2.y
    return sqrt(dx * dx + dy * dy)
}
