package com.yvr.widgetmanager.ui.components

import android.appwidget.AppWidgetHostView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import kotlin.math.max
import kotlin.math.min

/**
 * 可调整尺寸的Widget组件
 */
@Composable
fun ResizableWidget(
    widgetHostView: AppWidgetHostView,
    initialWidth: Dp,
    initialHeight: Dp,
    isResizeMode: Boolean,
    onSizeChanged: (Dp, Dp) -> Unit,
    modifier: Modifier = Modifier,
    minWidth: Dp = 100.dp,
    minHeight: Dp = 100.dp,
    maxWidth: Dp = 800.dp,
    maxHeight: Dp = 600.dp
) {
    var currentWidth by remember { mutableStateOf(initialWidth) }
    var currentHeight by remember { mutableStateOf(initialHeight) }
    
    val density = LocalDensity.current
    
    Box(
        modifier = modifier.size(currentWidth, currentHeight),
        contentAlignment = Alignment.Center
    ) {
        // Widget内容
        key(widgetHostView.hashCode()) {
            AndroidView(
                factory = { widgetHostView },
                modifier = Modifier.fillMaxSize(),
                update = { view ->
                    view.invalidate()
                    view.requestLayout()
                }
            )
        }
        
        // 调整模式下显示边框和调整点
        if (isResizeMode) {
            // 边框和调整点
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawResizeBorder(this)
            }

            // 四个边框中间的调整点，每个只支持垂直于边框的方向调整

            // 顶部中间调整点 - 只支持垂直调整（向上缩小，向下不变）
            ResizeHandle(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .offset(y = (-12).dp), // 向上偏移，让调整点在边框上
                onDrag = { dragAmount ->
                    val newHeight = with(density) {
                        (currentHeight.toPx() - dragAmount.y).coerceIn(minHeight.toPx(), maxHeight.toPx()).toDp()
                    }
                    currentHeight = newHeight
                    onSizeChanged(currentWidth, currentHeight)
                }
            )

            // 底部中间调整点 - 只支持垂直调整（向下扩大，向上缩小）
            ResizeHandle(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .offset(y = 12.dp), // 向下偏移，让调整点在边框上
                onDrag = { dragAmount ->
                    val newHeight = with(density) {
                        (currentHeight.toPx() + dragAmount.y).coerceIn(minHeight.toPx(), maxHeight.toPx()).toDp()
                    }
                    currentHeight = newHeight
                    onSizeChanged(currentWidth, currentHeight)
                }
            )

            // 左侧中间调整点 - 只支持水平调整（向左缩小，向右不变）
            ResizeHandle(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .offset(x = (-12).dp), // 向左偏移，让调整点在边框上
                onDrag = { dragAmount ->
                    val newWidth = with(density) {
                        (currentWidth.toPx() - dragAmount.x).coerceIn(minWidth.toPx(), maxWidth.toPx()).toDp()
                    }
                    currentWidth = newWidth
                    onSizeChanged(currentWidth, currentHeight)
                }
            )

            // 右侧中间调整点 - 只支持水平调整（向右扩大，向左缩小）
            ResizeHandle(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = 12.dp), // 向右偏移，让调整点在边框上
                onDrag = { dragAmount ->
                    val newWidth = with(density) {
                        (currentWidth.toPx() + dragAmount.x).coerceIn(minWidth.toPx(), maxWidth.toPx()).toDp()
                    }
                    currentWidth = newWidth
                    onSizeChanged(currentWidth, currentHeight)
                }
            )
        }
    }
}

/**
 * 绘制调整边框和调整点
 */
private fun drawResizeBorder(drawScope: DrawScope) {
    with(drawScope) {
        val strokeWidth = 3.dp.toPx()
        val borderColor = Color(0xFF2196F3) // 蓝色边框

        // 绘制边框
        drawRect(
            color = borderColor,
            topLeft = Offset(strokeWidth / 2, strokeWidth / 2),
            size = androidx.compose.ui.geometry.Size(
                size.width - strokeWidth,
                size.height - strokeWidth
            ),
            style = Stroke(width = strokeWidth)
        )

        // 绘制四个边中间的调整点
        val handleSize = 16.dp.toPx() // 调整点大小
        val handleThickness = 6.dp.toPx() // 调整点厚度

        // 顶部中间调整点（水平矩形）
        drawRect(
            color = borderColor,
            topLeft = Offset(
                size.width / 2 - handleSize / 2,
                -handleThickness / 2
            ),
            size = androidx.compose.ui.geometry.Size(handleSize, handleThickness)
        )

        // 底部中间调整点（水平矩形）
        drawRect(
            color = borderColor,
            topLeft = Offset(
                size.width / 2 - handleSize / 2,
                size.height - handleThickness / 2
            ),
            size = androidx.compose.ui.geometry.Size(handleSize, handleThickness)
        )

        // 左侧中间调整点（垂直矩形）
        drawRect(
            color = borderColor,
            topLeft = Offset(
                -handleThickness / 2,
                size.height / 2 - handleSize / 2
            ),
            size = androidx.compose.ui.geometry.Size(handleThickness, handleSize)
        )

        // 右侧中间调整点（垂直矩形）
        drawRect(
            color = borderColor,
            topLeft = Offset(
                size.width - handleThickness / 2,
                size.height / 2 - handleSize / 2
            ),
            size = androidx.compose.ui.geometry.Size(handleThickness, handleSize)
        )
    }
}

/**
 * 调整手柄组件 - 提供更大的触摸区域
 */
@Composable
private fun ResizeHandle(
    modifier: Modifier = Modifier,
    onDrag: (Offset) -> Unit
) {
    Box(
        modifier = modifier
            .size(32.dp) // 增大触摸区域，便于拖拽
            .pointerInput(Unit) {
                detectDragGestures(
                    onDragStart = { /* 可以在这里添加拖拽开始的反馈 */ },
                    onDragEnd = { /* 可以在这里添加拖拽结束的反馈 */ }
                ) { _, dragAmount ->
                    onDrag(dragAmount)
                }
            }
    )
}
