package com.yvr.widgetmanager.ui.screen

import android.appwidget.AppWidgetHostView
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.yvr.widgetmanager.WidgetDisplayActivity
import com.yvr.widgetmanager.manager.WidgetHostManager
import com.yvr.widgetmanager.model.AppWidgetInfo
import com.yvr.widgetmanager.ui.components.ResizableWidget
import com.yvr.widgetmanager.viewmodel.WidgetDisplayViewModel
import kotlinx.coroutines.delay

/**
 * Widget显示页面 - 只显示widget内容，透明背景
 */
@Composable
fun WidgetDisplayScreen(
    widgetInfo: AppWidgetInfo,
    widgetHostManager: WidgetHostManager,
    onBackClick: () -> Unit,
    viewModel: WidgetDisplayViewModel = viewModel(),
    restoredWidth: Int? = null,
    restoredHeight: Int? = null,
    activity: WidgetDisplayActivity? = null,
    skipConfiguration: Boolean = false
) {
    val widgetHostView by viewModel.widgetHostView
    val isResizeMode by viewModel.isResizeMode
    val widgetWidth by viewModel.widgetWidth
    val widgetHeight by viewModel.widgetHeight

    val density = LocalDensity.current
    val configuration = LocalConfiguration.current

    // 计算默认尺寸：宽度4/5，高度2/3
    val defaultWidthDp = (configuration.screenWidthDp * 4 / 5).dp
    val defaultHeightDp = (configuration.screenHeightDp * 2 / 3).dp

    // 处理返回键
    BackHandler {
        if (isResizeMode) {
            viewModel.exitResizeMode()
        } else {
            onBackClick()
        }
    }

    // 初始化ViewModel
    LaunchedEffect(widgetInfo.providerId) {
        viewModel.initialize(
            widgetHostManager = widgetHostManager,
            widgetInfo = widgetInfo,
            defaultWidth = with(density) { defaultWidthDp.toPx().toInt() },
            defaultHeight = with(density) { defaultHeightDp.toPx().toInt() },
            restoredWidth = restoredWidth,
            restoredHeight = restoredHeight,
            activity = activity,
            skipConfiguration = skipConfiguration
        )
    }

    // 清理资源
    DisposableEffect(Unit) {
        onDispose {
            viewModel.cleanup()
        }
    }

    // 透明背景的Box，支持长按进入调整模式，点击退出调整模式
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures(
                    onLongPress = { _ ->
                        if (!isResizeMode) {
                            viewModel.enterResizeMode()
                        }
                    },
                    onTap = { _ ->
                        if (isResizeMode) {
                            viewModel.exitResizeMode()
                        }
                    },
                    onDoubleTap = { _ ->
                        // 双击删除widget
                        viewModel.deleteCurrentWidget()
                        onBackClick()
                    }
                )
            },
        contentAlignment = Alignment.Center
    ) {
        widgetHostView?.let { hostView ->
            ResizableWidget(
                widgetHostView = hostView,
                initialWidth = with(density) { widgetWidth.toDp() },
                initialHeight = with(density) { widgetHeight.toDp() },
                isResizeMode = isResizeMode,
                onSizeChanged = { width, height ->
                    viewModel.updateWidgetSize(
                        with(density) { width.toPx().toInt() },
                        with(density) { height.toPx().toInt() }
                    )
                },
                minWidth = 100.dp,
                minHeight = 100.dp,
                maxWidth = configuration.screenWidthDp.dp * 0.9f,
                maxHeight = configuration.screenHeightDp.dp * 0.9f
            )
        }
    }
}
