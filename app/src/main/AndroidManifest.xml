<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permission to query installed packages -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WidgetManager"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.WidgetManager">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Widget显示Activity，使用透明主题 -->
        <activity
            android:name=".WidgetDisplayActivity"
            android:exported="true"
            android:screenOrientation="landscape"
            android:theme="@style/Theme.WidgetManager.Transparent"
            android:configChanges="locale|layoutDirection|orientation|density|screenLayout|screenSize|smallestScreenSize|fontScale"
            android:resizeableActivity="true"
            android:launchMode="standard">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <layout
                android:defaultWidth="1080px"
                android:defaultHeight="640px" />
        </activity>

        <!-- Widget恢复服务 -->
        <service
            android:name=".service.WidgetRestoreService"
            android:exported="false" />
    </application>

</manifest>